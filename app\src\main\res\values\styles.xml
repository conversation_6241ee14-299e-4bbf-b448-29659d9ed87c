<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="TransparentDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="CardButtonStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">120dp</item>
        <item name="android:background">@drawable/card_button_background</item>
        <item name="android:textColor">@color/card_text</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:foreground">?android:attr/selectableItemBackground</item>
    </style>
</resources>
package com.example.jubuddyai1.utils

import android.content.Context
import android.content.res.Configuration
import java.util.*

object LanguageManager {
    
    const val LANGUAGE_ENGLISH = "en"
    const val LANGUAGE_BENGALI = "bn"
    const val LANGUAGE_HINDI = "hi"
    
    private const val PREF_SELECTED_LANGUAGE = "selected_language"
    
    fun setLocale(context: Context, languageCode: String): Context {
        val locale = Locale(languageCode)
        Locale.setDefault(locale)
        
        val config = Configuration()
        config.setLocale(locale)
        
        // Save the selected language
        val prefs = context.getSharedPreferences("JUBuddyPrefs", Context.MODE_PRIVATE)
        prefs.edit().putString(PREF_SELECTED_LANGUAGE, languageCode).apply()
        
        return context.createConfigurationContext(config)
    }
    
    fun getSelectedLanguage(context: Context): String {
        val prefs = context.getSharedPreferences("JUBuddyPrefs", Context.MODE_PRIVATE)
        return prefs.getString(PREF_SELECTED_LANGUAGE, LANGUAGE_ENGLISH) ?: LANGUAGE_ENGLISH
    }
    
    fun applyLanguage(context: Context): Context {
        val selectedLanguage = getSelectedLanguage(context)
        return setLocale(context, selectedLanguage)
    }
    
    fun getLanguageName(languageCode: String): String {
        return when (languageCode) {
            LANGUAGE_ENGLISH -> "English"
            LANGUAGE_BENGALI -> "বাংলা"
            LANGUAGE_HINDI -> "हिंदी"
            else -> "English"
        }
    }
}

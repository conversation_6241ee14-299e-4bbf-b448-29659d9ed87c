<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/main_background"
    android:padding="16dp">

    <!-- First Row -->
    <LinearLayout
        android:id="@+id/firstRow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="2"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="32dp">

        <LinearLayout
            android:id="@+id/homeButton"
            style="@style/CardButtonStyle"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_home"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/home"
                android:textColor="@color/card_text"
                android:textSize="11sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/pyqButton"
            style="@style/CardButtonStyle"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_pyq"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/pyq"
                android:textColor="@color/card_text"
                android:textSize="11sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <!-- Second Row -->
    <LinearLayout
        android:id="@+id/secondRow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="2"
        app:layout_constraintTop_toBottomOf="@id/firstRow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp">

        <LinearLayout
            android:id="@+id/syllabusButton"
            style="@style/CardButtonStyle"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_syllabus"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/syllabus"
                android:textColor="@color/card_text"
                android:textSize="11sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/notesButton"
            style="@style/CardButtonStyle"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_notes"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/notes"
                android:textColor="@color/card_text"
                android:textSize="11sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <!-- Third Row -->
    <LinearLayout
        android:id="@+id/thirdRow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="2"
        app:layout_constraintTop_toBottomOf="@id/secondRow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp">

        <LinearLayout
            android:id="@+id/storeButton"
            style="@style/CardButtonStyle"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_store"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/store"
                android:textColor="@color/card_text"
                android:textSize="11sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/jubuddyButton"
            style="@style/CardButtonStyle"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_ai"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/jubuddy_ai"
                android:textColor="@color/card_text"
                android:textSize="11sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <!-- Fourth Row (Single Button) -->
    <LinearLayout
        android:id="@+id/fourthRow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:weightSum="3"
        app:layout_constraintTop_toBottomOf="@id/thirdRow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/adContainer"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="100dp">

        <!-- Empty space for centering -->
        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <LinearLayout
            android:id="@+id/settingsButton"
            android:layout_width="0dp"
            android:layout_height="90dp"
            android:layout_weight="1"
            android:background="@drawable/card_button_background"
            android:textColor="@color/card_text"
            android:textSize="11sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_margin="6dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground"
            android:orientation="vertical">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_settings"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/settings"
                android:textColor="@color/card_text"
                android:textSize="11sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Empty space for centering -->
        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/adContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>